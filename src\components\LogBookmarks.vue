<template>
  <div class="log-bookmarks h-full flex flex-col bg-white dark:bg-gray-800">
    <!-- 头部 -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">日志书签</h3>
      <div class="flex items-center space-x-2">
        <button
          @click="showAddBookmarkModal = true"
          class="btn-secondary text-sm"
        >
          <component :is="Plus" class="w-4 h-4 mr-1" />
          添加书签
        </button>
        <button
          @click="exportBookmarks"
          class="btn-secondary text-sm"
        >
          <component :is="Download" class="w-4 h-4 mr-1" />
          导出
        </button>
      </div>
    </div>
    
    <!-- 搜索和过滤 -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索书签..."
            class="input w-full"
          />
        </div>
        <select v-model="selectedCategory" class="input w-32">
          <option value="">全部分类</option>
          <option value="error">错误</option>
          <option value="performance">性能</option>
          <option value="security">安全</option>
          <option value="debug">调试</option>
          <option value="other">其他</option>
        </select>
      </div>
    </div>
    
    <!-- 书签列表 -->
    <div class="flex-1 overflow-y-auto p-4">
      <div v-if="filteredBookmarks.length === 0" class="text-center py-12">
        <component :is="Bookmark" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无书签</h3>
        <p class="text-gray-500 dark:text-gray-400">添加重要的日志条目到书签以便快速访问</p>
      </div>
      
      <div v-else class="space-y-4">
        <div
          v-for="bookmark in filteredBookmarks"
          :key="bookmark.id"
          class="bookmark-item p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-600 transition-colors cursor-pointer"
          @click="viewBookmark(bookmark)"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <!-- 书签头部 -->
              <div class="flex items-center space-x-3 mb-2">
                <div class="flex items-center space-x-2">
                  <div
                    class="w-3 h-3 rounded-full"
                    :class="getLevelColor(bookmark.logLevel)"
                  ></div>
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium"
                    :class="getCategoryClass(bookmark.category)"
                  >
                    {{ bookmark.category }}
                  </span>
                </div>
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  {{ formatTime(bookmark.createdAt) }}
                </span>
              </div>
              
              <!-- 书签标题 -->
              <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {{ bookmark.title }}
              </h4>
              
              <!-- 书签描述 -->
              <p v-if="bookmark.description" class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                {{ bookmark.description }}
              </p>
              
              <!-- 日志预览 -->
              <div class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600 p-3">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-2">
                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400">
                      {{ bookmark.logSource }}
                    </span>
                    <span class="text-xs text-gray-400 dark:text-gray-500">•</span>
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                      {{ formatTime(bookmark.logTimestamp) }}
                    </span>
                  </div>
                  <span
                    class="inline-flex items-center px-2 py-1 rounded text-xs font-medium"
                    :class="getLevelBadgeClass(bookmark.logLevel)"
                  >
                    {{ bookmark.logLevel.toUpperCase() }}
                  </span>
                </div>
                <div class="text-sm text-gray-900 dark:text-white font-mono bg-gray-50 dark:bg-gray-700 p-2 rounded">
                  {{ truncateMessage(bookmark.logMessage) }}
                </div>
              </div>
              
              <!-- 标签 -->
              <div v-if="bookmark.tags.length > 0" class="flex flex-wrap gap-2 mt-3">
                <span
                  v-for="tag in bookmark.tags"
                  :key="tag"
                  class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex items-center space-x-2 ml-4">
              <button
                @click.stop="editBookmark(bookmark)"
                class="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20"
                title="编辑书签"
              >
                <component :is="Edit" class="w-4 h-4" />
              </button>
              <button
                @click.stop="shareBookmark(bookmark)"
                class="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20"
                title="分享书签"
              >
                <component :is="Share2" class="w-4 h-4" />
              </button>
              <button
                @click.stop="deleteBookmark(bookmark.id)"
                class="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20"
                title="删除书签"
              >
                <component :is="Trash2" class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加书签模态框 -->
    <div v-if="showAddBookmarkModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showAddBookmarkModal = false">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4" @click.stop>
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">添加书签</h3>
          <button
            @click="showAddBookmarkModal = false"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
          >
            <component :is="X" class="w-6 h-6" />
          </button>
        </div>
        
        <div class="p-6 space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标题</label>
            <input
              v-model="newBookmark.title"
              type="text"
              class="input w-full"
              placeholder="输入书签标题"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">描述</label>
            <textarea
              v-model="newBookmark.description"
              class="input w-full h-20"
              placeholder="输入书签描述（可选）"
            ></textarea>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类</label>
            <select v-model="newBookmark.category" class="input w-full">
              <option value="error">错误</option>
              <option value="performance">性能</option>
              <option value="security">安全</option>
              <option value="debug">调试</option>
              <option value="other">其他</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标签</label>
            <input
              v-model="newBookmark.tagsInput"
              type="text"
              class="input w-full"
              placeholder="输入标签，用逗号分隔"
            />
          </div>
        </div>
        
        <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            @click="showAddBookmarkModal = false"
            class="btn-secondary"
          >
            取消
          </button>
          <button
            @click="saveBookmark"
            class="btn-primary"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { 
  Plus, 
  Download, 
  Bookmark, 
  Edit, 
  Share2, 
  Trash2, 
  X 
} from 'lucide-vue-next'
import { format } from 'date-fns'
import { useAppStore } from '@/stores/appStore'

const appStore = useAppStore()

// 状态
const searchQuery = ref('')
const selectedCategory = ref('')
const showAddBookmarkModal = ref(false)

// 书签数据
const bookmarks = ref([])

// 新书签表单
const newBookmark = ref({
  title: '',
  description: '',
  category: 'other',
  tagsInput: ''
})

// 计算属性
const filteredBookmarks = computed(() => {
  return bookmarks.value.filter(bookmark => {
    const matchesSearch = !searchQuery.value || 
      bookmark.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      bookmark.description.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      bookmark.logMessage.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesCategory = !selectedCategory.value || bookmark.category === selectedCategory.value
    
    return matchesSearch && matchesCategory
  })
})

// 方法
const getLevelColor = (level) => {
  const colors = {
    error: 'bg-red-500',
    warn: 'bg-yellow-500',
    info: 'bg-blue-500',
    debug: 'bg-gray-500',
    trace: 'bg-purple-500'
  }
  return colors[level] || 'bg-gray-500'
}

const getLevelBadgeClass = (level) => {
  const classes = {
    error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    warn: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    info: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    debug: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    trace: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
  }
  return classes[level] || classes.info
}

const getCategoryClass = (category) => {
  const classes = {
    error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    performance: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    security: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    debug: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    other: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
  }
  return classes[category] || classes.other
}

const formatTime = (timestamp) => {
  return format(new Date(timestamp), 'MM-dd HH:mm')
}

const truncateMessage = (message) => {
  return message.length > 200 ? message.substring(0, 200) + '...' : message
}

const saveBookmark = () => {
  const bookmark = {
    id: Date.now(),
    title: newBookmark.value.title,
    description: newBookmark.value.description,
    category: newBookmark.value.category,
    tags: newBookmark.value.tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag),
    createdAt: new Date(),
    logLevel: 'info', // 这里应该从实际选中的日志获取
    logSource: 'system',
    logTimestamp: new Date(),
    logMessage: '示例日志消息...'
  }
  
  bookmarks.value.unshift(bookmark)
  saveBookmarksToStorage()
  
  // 重置表单
  newBookmark.value = {
    title: '',
    description: '',
    category: 'other',
    tagsInput: ''
  }
  
  showAddBookmarkModal.value = false
  
  appStore.addNotification({
    type: 'success',
    message: '书签已保存'
  })
}

const viewBookmark = (bookmark) => {
  appStore.addNotification({
    type: 'info',
    message: `查看书签: ${bookmark.title}`
  })
}

const editBookmark = (bookmark) => {
  appStore.addNotification({
    type: 'info',
    message: `编辑书签: ${bookmark.title}`
  })
}

const shareBookmark = (bookmark) => {
  const shareData = {
    title: bookmark.title,
    text: bookmark.description,
    url: window.location.href
  }
  
  if (navigator.share) {
    navigator.share(shareData)
  } else {
    // 复制到剪贴板
    navigator.clipboard.writeText(JSON.stringify(shareData, null, 2))
    appStore.addNotification({
      type: 'success',
      message: '书签信息已复制到剪贴板'
    })
  }
}

const deleteBookmark = (id) => {
  if (confirm('确定要删除这个书签吗？')) {
    bookmarks.value = bookmarks.value.filter(bookmark => bookmark.id !== id)
    saveBookmarksToStorage()
    
    appStore.addNotification({
      type: 'success',
      message: '书签已删除'
    })
  }
}

const exportBookmarks = () => {
  const data = {
    exportTime: new Date().toISOString(),
    bookmarks: bookmarks.value
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `log-bookmarks-${format(new Date(), 'yyyy-MM-dd')}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  appStore.addNotification({
    type: 'success',
    message: '书签已导出'
  })
}

const saveBookmarksToStorage = () => {
  localStorage.setItem('logBookmarks', JSON.stringify(bookmarks.value))
}

const loadBookmarksFromStorage = () => {
  const saved = localStorage.getItem('logBookmarks')
  if (saved) {
    try {
      bookmarks.value = JSON.parse(saved)
    } catch (error) {
      console.error('Failed to load bookmarks:', error)
    }
  }
}

// 生命周期
onMounted(() => {
  loadBookmarksFromStorage()
  
  // 生成一些示例书签
  if (bookmarks.value.length === 0) {
    bookmarks.value = [
      {
        id: 1,
        title: '数据库连接超时错误',
        description: '生产环境数据库连接频繁超时，需要调查原因',
        category: 'error',
        tags: ['database', 'timeout', 'production'],
        createdAt: new Date(Date.now() - 86400000),
        logLevel: 'error',
        logSource: 'mysql',
        logTimestamp: new Date(Date.now() - 86400000),
        logMessage: 'Database connection timeout after 30 seconds. Connection pool exhausted.'
      },
      {
        id: 2,
        title: 'API响应时间异常',
        description: '用户登录API响应时间超过5秒',
        category: 'performance',
        tags: ['api', 'performance', 'login'],
        createdAt: new Date(Date.now() - 172800000),
        logLevel: 'warn',
        logSource: 'api-server',
        logTimestamp: new Date(Date.now() - 172800000),
        logMessage: 'POST /api/auth/login took 5234ms to complete. Threshold: 1000ms'
      }
    ]
  }
})
</script>

<style scoped>
.bookmark-item {
  transition: all 0.2s ease;
}

.bookmark-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
