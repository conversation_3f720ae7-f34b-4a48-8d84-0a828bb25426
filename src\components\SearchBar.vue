<template>
  <div class="relative">
    <!-- 搜索输入框 -->
    <div class="relative group">
      <!-- 搜索图标 -->
      <div class="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none z-10">
        <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-sm group-focus-within:shadow-md transition-all duration-300">
          <component :is="Search" class="h-4 w-4 text-white" />
        </div>
      </div>

      <!-- 主搜索框 -->
      <input
        v-model="searchQuery"
        @input="handleSearch"
        @focus="showSuggestions = true"
        @keydown="handleKeydown"
        type="text"
        placeholder="搜索日志内容、来源、服务... 支持正则表达式和高级语法"
        class="w-full pl-16 pr-16 py-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-2 border-gray-200/50 dark:border-gray-600/50 rounded-2xl text-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 shadow-lg hover:shadow-xl focus:shadow-2xl font-medium"
      />

      <!-- 右侧操作区 -->
      <div class="absolute inset-y-0 right-0 pr-6 flex items-center space-x-2">
        <!-- 搜索状态指示器 -->
        <div v-if="isSearching" class="flex items-center">
          <div class="relative">
            <div class="animate-spin rounded-full h-5 w-5 border-2 border-blue-200 border-t-blue-500"></div>
            <div class="absolute inset-0 rounded-full h-5 w-5 border-2 border-transparent border-t-blue-300 animate-ping"></div>
          </div>
        </div>

        <!-- 清除按钮 -->
        <button
          v-if="searchQuery && !isSearching"
          @click="clearSearch"
          class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-200 group"
          title="清除搜索"
        >
          <component :is="X" class="h-5 w-5 group-hover:scale-110 transition-transform duration-200" />
        </button>

        <!-- 搜索快捷键提示 -->
        <div class="hidden lg:flex items-center space-x-1 text-xs text-gray-400 dark:text-gray-500">
          <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded border border-gray-300 dark:border-gray-600">Ctrl</kbd>
          <span>+</span>
          <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded border border-gray-300 dark:border-gray-600">K</kbd>
        </div>
      </div>
    </div>

    <!-- 快速搜索标签 -->
    <div v-if="!searchQuery && !showSuggestions" class="mt-6">
      <div class="flex items-center space-x-4 mb-4">
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">快速搜索</span>
        </div>
        <div class="flex-1 h-px bg-gradient-to-r from-gray-200 to-transparent dark:from-gray-600"></div>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
        <button
          v-for="tag in quickSearchTags"
          :key="tag.label"
          @click="applyQuickSearch(tag.query)"
          class="group flex flex-col items-center p-4 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border border-gray-200/50 dark:border-gray-600/50 rounded-xl hover:bg-white dark:hover:bg-gray-700 hover:shadow-lg hover:scale-105 transition-all duration-300"
        >
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform duration-300">{{ tag.icon }}</div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">{{ tag.label }}</span>
        </button>
      </div>
    </div>

    <!-- 搜索建议下拉框 -->
    <div
      v-if="showSuggestions && (suggestions.length > 0 || recentSearches.length > 0)"
      class="absolute top-full left-0 right-0 mt-4 bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-2xl z-50 overflow-hidden"
    >
      <!-- 最近搜索 -->
      <div v-if="recentSearches.length > 0 && !searchQuery" class="p-3 border-b border-gray-100 dark:border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <span class="text-xs font-medium text-gray-500 dark:text-gray-400">最近搜索</span>
          <button
            @click="clearRecentSearches"
            class="text-xs text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
          >
            清除
          </button>
        </div>
        <div class="space-y-1">
          <div
            v-for="(recent, index) in recentSearches.slice(0, 3)"
            :key="index"
            @click="applySuggestion(recent)"
            class="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer group"
          >
            <component :is="Clock" class="w-4 h-4 mr-2 text-gray-400" />
            <span class="flex-1">{{ recent }}</span>
            <component :is="ArrowUpRight" class="w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
        </div>
      </div>

      <!-- 搜索建议 -->
      <div v-if="suggestions.length > 0" class="p-3">
        <div class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">搜索建议</div>
        <div class="space-y-1">
          <div
            v-for="(suggestion, index) in suggestions"
            :key="index"
            @click="applySuggestion(suggestion)"
            :class="[
              'flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg cursor-pointer group transition-colors duration-200',
              selectedSuggestionIndex === index ? 'bg-blue-50 dark:bg-blue-900/30' : ''
            ]"
          >
            <component :is="Search" class="w-4 h-4 mr-2 text-gray-400" />
            <span class="flex-1" v-html="highlightSuggestion(suggestion)"></span>
            <component :is="ArrowUpRight" class="w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
        </div>
      </div>

      <!-- 搜索语法提示 -->
      <div class="p-3 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-100 dark:border-gray-700">
        <div class="text-xs text-gray-500 dark:text-gray-400">
          <div class="font-medium mb-1">搜索语法:</div>
          <div class="space-y-1">
            <div><code class="px-1 bg-gray-200 dark:bg-gray-700 rounded">level:error</code> - 按级别搜索</div>
            <div><code class="px-1 bg-gray-200 dark:bg-gray-700 rounded">source:nginx</code> - 按来源搜索</div>
            <div><code class="px-1 bg-gray-200 dark:bg-gray-700 rounded">/regex/</code> - 正则表达式搜索</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useLogStore } from '@/stores/logStore'
import { useDebounceFn } from '@vueuse/core'
import { Search, X, Clock, ArrowUpRight } from 'lucide-vue-next'

const logStore = useLogStore()

// 组件状态
const searchQuery = ref(logStore.searchQuery)
const showSuggestions = ref(false)
const isSearching = ref(false)
const selectedSuggestionIndex = ref(-1)
const recentSearches = ref(JSON.parse(localStorage.getItem('logSearchHistory') || '[]'))

// 快速搜索标签
const quickSearchTags = [
  { label: '错误', query: 'level:error', icon: '🔴' },
  { label: '警告', query: 'level:warn', icon: '🟡' },
  { label: '数据库', query: 'source:mysql', icon: '🗄️' },
  { label: 'API', query: 'service:api', icon: '🔌' },
  { label: '超时', query: 'timeout', icon: '⏰' },
  { label: '失败', query: 'failed', icon: '❌' }
]

// 搜索建议
const searchSuggestions = [
  'level:error',
  'level:warn',
  'level:info',
  'source:nginx',
  'source:mysql',
  'source:redis',
  'service:api',
  'service:web',
  'timeout',
  'failed',
  'connection',
  'authentication',
  'database',
  'memory',
  'disk space',
  'ssl certificate',
  'permission denied',
  'not found',
  'internal error',
  'bad request'
]

// 计算属性
const suggestions = computed(() => {
  if (!searchQuery.value || searchQuery.value.length < 2) {
    return []
  }

  const query = searchQuery.value.toLowerCase()
  return searchSuggestions
    .filter(suggestion => suggestion.toLowerCase().includes(query))
    .slice(0, 6)
})

// 防抖搜索
const debouncedSearch = useDebounceFn((query) => {
  isSearching.value = true
  logStore.setSearchQuery(query)

  // 模拟搜索延迟
  setTimeout(() => {
    isSearching.value = false
  }, 300)
}, 300)

// 方法
const handleSearch = () => {
  showSuggestions.value = true
  debouncedSearch(searchQuery.value)
}

const handleKeydown = (event) => {
  if (!showSuggestions.value) return

  const totalSuggestions = suggestions.value.length

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedSuggestionIndex.value = Math.min(selectedSuggestionIndex.value + 1, totalSuggestions - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedSuggestionIndex.value = Math.max(selectedSuggestionIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (selectedSuggestionIndex.value >= 0 && selectedSuggestionIndex.value < totalSuggestions) {
        applySuggestion(suggestions.value[selectedSuggestionIndex.value])
      } else {
        applySuggestion(searchQuery.value)
      }
      break
    case 'Escape':
      showSuggestions.value = false
      selectedSuggestionIndex.value = -1
      break
  }
}

const clearSearch = () => {
  searchQuery.value = ''
  showSuggestions.value = false
  selectedSuggestionIndex.value = -1
  logStore.setSearchQuery('')
}

const applySuggestion = (suggestion) => {
  searchQuery.value = suggestion
  showSuggestions.value = false
  selectedSuggestionIndex.value = -1
  logStore.setSearchQuery(suggestion)

  // 添加到搜索历史
  addToSearchHistory(suggestion)
}

const applyQuickSearch = (query) => {
  searchQuery.value = query
  logStore.setSearchQuery(query)
  addToSearchHistory(query)
}

const addToSearchHistory = (query) => {
  if (!query.trim()) return

  const history = recentSearches.value.filter(item => item !== query)
  history.unshift(query)
  recentSearches.value = history.slice(0, 10) // 保留最近10条

  localStorage.setItem('logSearchHistory', JSON.stringify(recentSearches.value))
}

const clearRecentSearches = () => {
  recentSearches.value = []
  localStorage.removeItem('logSearchHistory')
}

const highlightSuggestion = (suggestion) => {
  if (!searchQuery.value) return suggestion

  const query = searchQuery.value.toLowerCase()
  const regex = new RegExp(`(${query})`, 'gi')
  return suggestion.replace(regex, '<mark class="bg-blue-200 dark:bg-blue-800 px-1 rounded">$1</mark>')
}

// 监听器
watch(() => logStore.searchQuery, (newQuery) => {
  if (newQuery !== searchQuery.value) {
    searchQuery.value = newQuery
  }
})

// 点击外部隐藏建议
const handleClickOutside = (event) => {
  if (!event.target.closest('.relative')) {
    showSuggestions.value = false
    selectedSuggestionIndex.value = -1
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
