<template>
  <div class="space-y-4 p-6">
    <div
      v-for="(log, index) in logStore.paginatedLogs"
      :key="log.id"
      class="group relative bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 hover:bg-white dark:hover:bg-gray-700 hover:shadow-xl hover:scale-[1.02] transition-all duration-300 cursor-pointer border border-gray-200/50 dark:border-gray-700/50"
      @click="selectLog(log)"
    >
      <div class="flex items-start space-x-4">
        <!-- 日志级别指示器 -->
        <div class="flex-shrink-0 mt-2">
          <div class="relative">
            <div
              class="w-6 h-6 rounded-xl shadow-lg flex items-center justify-center"
              :class="getLevelColor(log.level)"
            >
              <span class="text-white text-xs font-bold">{{ getLevelIcon(log.level) }}</span>
            </div>
            <div
              class="absolute inset-0 w-6 h-6 rounded-xl animate-ping opacity-30"
              :class="getLevelColor(log.level)"
              v-if="log.level === 'error'"
            ></div>
          </div>
        </div>

        <!-- 日志内容 -->
        <div class="flex-1 min-w-0">
          <!-- 头部信息 -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <!-- 级别标签 -->
              <div
                class="inline-flex items-center px-3 py-1.5 rounded-xl text-sm font-bold uppercase tracking-wide shadow-sm"
                :class="getLevelBadgeClass(log.level)"
              >
                {{ log.level }}
              </div>

              <!-- 来源标签 -->
              <div class="inline-flex items-center px-3 py-1.5 rounded-xl text-sm font-medium bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 dark:from-gray-700 dark:to-gray-600 dark:text-gray-300 shadow-sm">
                <component :is="Database" class="w-4 h-4 mr-2" />
                {{ log.source }}
              </div>

              <!-- 服务标签 -->
              <div class="inline-flex items-center px-3 py-1.5 rounded-xl text-sm font-medium bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 dark:from-blue-900/50 dark:to-blue-800/50 dark:text-blue-300 shadow-sm">
                <component :is="Server" class="w-4 h-4 mr-2" />
                {{ log.service }}
              </div>
            </div>

            <!-- 时间戳 -->
            <div class="flex items-center space-x-2 px-3 py-1.5 bg-gray-100 dark:bg-gray-700 rounded-xl">
              <component :is="Clock" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
              <span class="font-mono text-sm font-medium text-gray-700 dark:text-gray-300">{{ formatTimestamp(log.timestamp) }}</span>
            </div>
          </div>

          <!-- 日志消息 -->
          <div class="log-entry mb-4">
            <div class="font-mono text-base bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-xl p-4 border border-gray-200 dark:border-gray-600 shadow-inner leading-relaxed">
              <span class="text-gray-900 dark:text-gray-100" v-html="highlightSearchTerm(log.message)"></span>
            </div>
          </div>

          <!-- 元数据 -->
          <div v-if="log.metadata" class="flex flex-wrap items-center gap-3">
            <div v-if="log.metadata.userId" class="inline-flex items-center px-3 py-1.5 rounded-xl text-sm font-medium bg-gradient-to-r from-purple-100 to-purple-200 text-purple-700 dark:from-purple-900/50 dark:to-purple-800/50 dark:text-purple-300 shadow-sm">
              <component :is="User" class="w-4 h-4 mr-2" />
              用户: {{ log.metadata.userId }}
            </div>
            <div v-if="log.metadata.requestId" class="inline-flex items-center px-3 py-1.5 rounded-xl text-sm font-medium bg-gradient-to-r from-green-100 to-green-200 text-green-700 dark:from-green-900/50 dark:to-green-800/50 dark:text-green-300 shadow-sm">
              <component :is="Hash" class="w-4 h-4 mr-2" />
              请求: {{ log.metadata.requestId }}
            </div>
            <div v-if="log.metadata.ip" class="inline-flex items-center px-3 py-1.5 rounded-xl text-sm font-medium bg-gradient-to-r from-orange-100 to-orange-200 text-orange-700 dark:from-orange-900/50 dark:to-orange-800/50 dark:text-orange-300 shadow-sm">
              <component :is="Globe" class="w-4 h-4 mr-2" />
              IP: {{ log.metadata.ip }}
            </div>
            <div v-if="log.metadata.responseTime" class="inline-flex items-center px-3 py-1.5 rounded-xl text-sm font-medium bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-700 dark:from-yellow-900/50 dark:to-yellow-800/50 dark:text-yellow-300 shadow-sm">
              <component :is="Zap" class="w-4 h-4 mr-2" />
              {{ log.metadata.responseTime }}
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-all duration-300">
          <div class="flex flex-col space-y-2">
            <button
              @click.stop="copyLog(log)"
              class="p-3 text-gray-400 hover:text-white hover:bg-blue-500 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-110"
              title="复制日志条目"
            >
              <component :is="Copy" class="w-5 h-5" />
            </button>
            <button
              @click.stop="shareLog(log)"
              class="p-3 text-gray-400 hover:text-white hover:bg-green-500 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-110"
              title="分享日志"
            >
              <component :is="Share2" class="w-5 h-5" />
            </button>
            <button
              @click.stop="bookmarkLog(log)"
              class="p-3 text-gray-400 hover:text-white hover:bg-yellow-500 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-110"
              title="添加书签"
            >
              <component :is="Bookmark" class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      <!-- 序号标识 -->
      <div class="absolute top-4 left-4 w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-500 rounded-lg flex items-center justify-center shadow-sm">
        <span class="text-white text-xs font-bold">{{ (logStore.currentPage - 1) * logStore.pageSize + index + 1 }}</span>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div
      v-if="logStore.paginatedLogs.length === 0"
      class="flex items-center justify-center min-h-[400px]"
    >
      <div class="max-w-lg mx-auto text-center">
        <!-- 空状态图标 -->
        <div class="relative mb-8">
          <div class="w-32 h-32 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-3xl flex items-center justify-center shadow-2xl">
            <component :is="FileText" class="w-16 h-16 text-gray-400 dark:text-gray-500" />
          </div>
          <div class="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
            <component :is="Search" class="w-4 h-4 text-white" />
          </div>
        </div>

        <!-- 空状态标题 -->
        <h3 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          {{ logStore.logs.length === 0 ? '暂无日志数据' : '未找到匹配的日志' }}
        </h3>

        <!-- 空状态描述 -->
        <p class="text-lg text-gray-500 dark:text-gray-400 mb-8 leading-relaxed max-w-md mx-auto">
          {{ logStore.logs.length === 0
            ? '系统尚未收集到任何日志数据，请检查日志源配置或等待数据采集。'
            : '当前过滤条件下没有找到匹配的日志条目，请尝试调整搜索关键词或过滤器设置。'
          }}
        </p>

        <!-- 操作按钮 -->
        <div class="flex flex-col sm:flex-row justify-center gap-4">
          <button
            v-if="logStore.logs.length > 0"
            @click="clearFilters"
            class="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
          >
            <component :is="RotateCcw" class="w-5 h-5 mr-3" />
            清除过滤器
          </button>
          <button
            @click="refreshData"
            class="inline-flex items-center justify-center px-6 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl hover:bg-gray-50 dark:hover:bg-gray-700 transform hover:scale-105 transition-all duration-300"
          >
            <component :is="RefreshCw" class="w-5 h-5 mr-3" />
            刷新数据
          </button>
        </div>

        <!-- 提示信息 -->
        <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
          <p class="text-sm text-blue-700 dark:text-blue-300">
            💡 提示：您可以使用高级搜索语法，如 <code class="px-2 py-1 bg-blue-100 dark:bg-blue-800 rounded">level:error</code> 或 <code class="px-2 py-1 bg-blue-100 dark:bg-blue-800 rounded">source:nginx</code>
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- 日志详情模态框 -->
  <LogDetailModal
    v-if="selectedLog"
    :log="selectedLog"
    @close="selectedLog = null"
  />
</template>

<script setup>
import { ref } from 'vue'
import { useLogStore } from '@/stores/logStore'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import {
  Copy,
  FileText,
  Database,
  Server,
  Clock,
  User,
  Hash,
  Globe,
  Zap,
  Share2,
  Bookmark,
  RotateCcw,
  RefreshCw
} from 'lucide-vue-next'
import LogDetailModal from './LogDetailModal.vue'

const logStore = useLogStore()
const appStore = useAppStore()

const selectedLog = ref(null)

// 样式相关方法
const getLevelColor = (level) => {
  const colors = {
    error: 'bg-gradient-to-br from-red-500 to-red-600 shadow-red-300 dark:shadow-red-900/50',
    warn: 'bg-gradient-to-br from-yellow-500 to-yellow-600 shadow-yellow-300 dark:shadow-yellow-900/50',
    info: 'bg-gradient-to-br from-blue-500 to-blue-600 shadow-blue-300 dark:shadow-blue-900/50',
    debug: 'bg-gradient-to-br from-gray-500 to-gray-600 shadow-gray-300 dark:shadow-gray-900/50',
    trace: 'bg-gradient-to-br from-purple-500 to-purple-600 shadow-purple-300 dark:shadow-purple-900/50'
  }
  return colors[level] || 'bg-gradient-to-br from-gray-500 to-gray-600'
}

const getLevelBadgeClass = (level) => {
  const classes = {
    error: 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 dark:from-red-900/50 dark:to-red-800/50 dark:text-red-200 border border-red-300 dark:border-red-700',
    warn: 'bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 dark:from-yellow-900/50 dark:to-yellow-800/50 dark:text-yellow-200 border border-yellow-300 dark:border-yellow-700',
    info: 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 dark:from-blue-900/50 dark:to-blue-800/50 dark:text-blue-200 border border-blue-300 dark:border-blue-700',
    debug: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 dark:from-gray-900/50 dark:to-gray-800/50 dark:text-gray-200 border border-gray-300 dark:border-gray-700',
    trace: 'bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 dark:from-purple-900/50 dark:to-purple-800/50 dark:text-purple-200 border border-purple-300 dark:border-purple-700'
  }
  return classes[level] || classes.info
}

const getLevelIcon = (level) => {
  const icons = {
    error: 'E',
    warn: 'W',
    info: 'I',
    debug: 'D',
    trace: 'T'
  }
  return icons[level] || 'I'
}

// 工具方法
const formatTimestamp = (timestamp) => {
  return format(new Date(timestamp), 'MM-dd HH:mm:ss.SSS')
}

const highlightSearchTerm = (message) => {
  if (!logStore.searchQuery) {
    return message
  }

  const query = logStore.searchQuery.toLowerCase()
  const regex = new RegExp(`(${query})`, 'gi')
  return message.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>')
}

// 操作方法
const selectLog = (log) => {
  selectedLog.value = log
}

const copyLog = async (log) => {
  const logText = `[${log.timestamp}] ${log.level.toUpperCase()} ${log.source}/${log.service}: ${log.message}`

  try {
    await navigator.clipboard.writeText(logText)
    appStore.addNotification({
      type: 'success',
      message: '📋 日志条目已复制到剪贴板'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '❌ 复制日志条目失败'
    })
  }
}

const shareLog = async (log) => {
  const logText = `[${log.timestamp}] ${log.level.toUpperCase()} ${log.source}/${log.service}: ${log.message}`

  try {
    await navigator.clipboard.writeText(logText)
    appStore.addNotification({
      type: 'success',
      message: '🔗 日志链接已复制，可以分享给团队成员'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '❌ 分享失败'
    })
  }
}

const bookmarkLog = (log) => {
  // 这里可以实现书签功能
  appStore.addNotification({
    type: 'success',
    message: '⭐ 日志已添加到书签'
  })
}

const clearFilters = () => {
  logStore.setSearchQuery('')
  logStore.setLogLevel('all')
  logStore.setSelectedSources([])
}

const refreshData = () => {
  appStore.addNotification({
    type: 'info',
    message: '🔄 正在刷新日志数据...'
  })
}
</script>

<style scoped>
mark {
  padding: 0 2px;
  border-radius: 2px;
}
</style>
