<template>
  <div class="divide-y divide-gray-100 dark:divide-gray-700/50">
    <div
      v-for="(log, index) in logStore.paginatedLogs"
      :key="log.id"
      class="group relative p-5 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-transparent dark:hover:from-gray-700/30 dark:hover:to-transparent transition-all duration-200 cursor-pointer border-l-4 border-transparent hover:border-blue-400"
      @click="selectLog(log)"
    >
      <div class="flex items-start space-x-4">
        <!-- 日志级别指示器 -->
        <div class="flex-shrink-0 mt-1.5">
          <div class="relative">
            <div
              class="w-4 h-4 rounded-full shadow-sm"
              :class="getLevelColor(log.level)"
            ></div>
            <div
              class="absolute inset-0 w-4 h-4 rounded-full animate-ping opacity-20"
              :class="getLevelColor(log.level)"
              v-if="log.level === 'error'"
            ></div>
          </div>
        </div>

        <!-- 日志内容 -->
        <div class="flex-1 min-w-0">
          <!-- 头部信息 -->
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3">
              <!-- 级别标签 -->
              <span
                class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold uppercase tracking-wide"
                :class="getLevelBadgeClass(log.level)"
              >
                {{ getLevelIcon(log.level) }} {{ log.level }}
              </span>

              <!-- 来源标签 -->
              <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600">
                <component :is="Database" class="w-3 h-3 mr-1" />
                {{ log.source }}
              </span>

              <!-- 服务标签 -->
              <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 border border-blue-200 dark:border-blue-700">
                <component :is="Server" class="w-3 h-3 mr-1" />
                {{ log.service }}
              </span>
            </div>

            <!-- 时间戳 -->
            <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <component :is="Clock" class="w-4 h-4" />
              <span class="font-mono">{{ formatTimestamp(log.timestamp) }}</span>
            </div>
          </div>

          <!-- 日志消息 -->
          <div class="log-entry text-gray-900 dark:text-gray-100 mb-3 leading-relaxed">
            <div class="font-mono text-sm bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
              <span v-html="highlightSearchTerm(log.message)"></span>
            </div>
          </div>

          <!-- 元数据 -->
          <div v-if="log.metadata" class="flex flex-wrap items-center gap-3 text-xs">
            <div v-if="log.metadata.userId" class="inline-flex items-center px-2 py-1 rounded-md bg-purple-50 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300 border border-purple-200 dark:border-purple-700">
              <component :is="User" class="w-3 h-3 mr-1" />
              用户: {{ log.metadata.userId }}
            </div>
            <div v-if="log.metadata.requestId" class="inline-flex items-center px-2 py-1 rounded-md bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-300 border border-green-200 dark:border-green-700">
              <component :is="Hash" class="w-3 h-3 mr-1" />
              请求: {{ log.metadata.requestId }}
            </div>
            <div v-if="log.metadata.ip" class="inline-flex items-center px-2 py-1 rounded-md bg-orange-50 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300 border border-orange-200 dark:border-orange-700">
              <component :is="Globe" class="w-3 h-3 mr-1" />
              IP: {{ log.metadata.ip }}
            </div>
            <div v-if="log.metadata.responseTime" class="inline-flex items-center px-2 py-1 rounded-md bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-700">
              <component :is="Zap" class="w-3 h-3 mr-1" />
              {{ log.metadata.responseTime }}
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div class="flex items-center space-x-2">
            <button
              @click.stop="copyLog(log)"
              class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200"
              title="复制日志条目"
            >
              <component :is="Copy" class="w-4 h-4" />
            </button>
            <button
              @click.stop="shareLog(log)"
              class="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/30 rounded-lg transition-all duration-200"
              title="分享日志"
            >
              <component :is="Share2" class="w-4 h-4" />
            </button>
            <button
              @click.stop="bookmarkLog(log)"
              class="p-2 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 dark:hover:bg-yellow-900/30 rounded-lg transition-all duration-200"
              title="添加书签"
            >
              <component :is="Bookmark" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      <!-- 行号 -->
      <div class="absolute left-0 top-0 bottom-0 w-1 bg-gray-200 dark:bg-gray-700 opacity-30">
        <div class="absolute left-2 top-2 text-xs text-gray-400 font-mono">
          {{ (logStore.currentPage - 1) * logStore.pageSize + index + 1 }}
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div
      v-if="logStore.paginatedLogs.length === 0"
      class="p-16 text-center"
    >
      <div class="max-w-md mx-auto">
        <div class="w-20 h-20 mx-auto mb-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <component :is="FileText" class="w-10 h-10 text-gray-400" />
        </div>
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
          {{ logStore.logs.length === 0 ? '暂无日志数据' : '未找到匹配的日志' }}
        </h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6 leading-relaxed">
          {{ logStore.logs.length === 0
            ? '系统尚未收集到任何日志数据，请检查日志源配置或等待数据采集。'
            : '当前过滤条件下没有找到匹配的日志条目，请尝试调整搜索关键词或过滤器设置。'
          }}
        </p>
        <div class="flex justify-center space-x-4">
          <button
            v-if="logStore.logs.length > 0"
            @click="clearFilters"
            class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
          >
            <component :is="RotateCcw" class="w-4 h-4 mr-2" />
            清除过滤器
          </button>
          <button
            @click="refreshData"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
          >
            <component :is="RefreshCw" class="w-4 h-4 mr-2" />
            刷新数据
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 日志详情模态框 -->
  <LogDetailModal
    v-if="selectedLog"
    :log="selectedLog"
    @close="selectedLog = null"
  />
</template>

<script setup>
import { ref } from 'vue'
import { useLogStore } from '@/stores/logStore'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import {
  Copy,
  FileText,
  Database,
  Server,
  Clock,
  User,
  Hash,
  Globe,
  Zap,
  Share2,
  Bookmark,
  RotateCcw,
  RefreshCw
} from 'lucide-vue-next'
import LogDetailModal from './LogDetailModal.vue'

const logStore = useLogStore()
const appStore = useAppStore()

const selectedLog = ref(null)

// 样式相关方法
const getLevelColor = (level) => {
  const colors = {
    error: 'bg-red-500 shadow-red-200 dark:shadow-red-900/50',
    warn: 'bg-yellow-500 shadow-yellow-200 dark:shadow-yellow-900/50',
    info: 'bg-blue-500 shadow-blue-200 dark:shadow-blue-900/50',
    debug: 'bg-gray-500 shadow-gray-200 dark:shadow-gray-900/50',
    trace: 'bg-purple-500 shadow-purple-200 dark:shadow-purple-900/50'
  }
  return colors[level] || 'bg-gray-500'
}

const getLevelBadgeClass = (level) => {
  const classes = {
    error: 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200 border-red-200 dark:border-red-700',
    warn: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200 border-yellow-200 dark:border-yellow-700',
    info: 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200 border-blue-200 dark:border-blue-700',
    debug: 'bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-200 border-gray-200 dark:border-gray-700',
    trace: 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200 border-purple-200 dark:border-purple-700'
  }
  return classes[level] || classes.info
}

const getLevelIcon = (level) => {
  const icons = {
    error: '🔴',
    warn: '🟡',
    info: '🔵',
    debug: '⚪',
    trace: '🟣'
  }
  return icons[level] || '⚪'
}

// 工具方法
const formatTimestamp = (timestamp) => {
  return format(new Date(timestamp), 'MM-dd HH:mm:ss.SSS')
}

const highlightSearchTerm = (message) => {
  if (!logStore.searchQuery) {
    return message
  }

  const query = logStore.searchQuery.toLowerCase()
  const regex = new RegExp(`(${query})`, 'gi')
  return message.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>')
}

// 操作方法
const selectLog = (log) => {
  selectedLog.value = log
}

const copyLog = async (log) => {
  const logText = `[${log.timestamp}] ${log.level.toUpperCase()} ${log.source}/${log.service}: ${log.message}`

  try {
    await navigator.clipboard.writeText(logText)
    appStore.addNotification({
      type: 'success',
      message: '📋 日志条目已复制到剪贴板'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '❌ 复制日志条目失败'
    })
  }
}

const shareLog = async (log) => {
  const logText = `[${log.timestamp}] ${log.level.toUpperCase()} ${log.source}/${log.service}: ${log.message}`

  try {
    await navigator.clipboard.writeText(logText)
    appStore.addNotification({
      type: 'success',
      message: '🔗 日志链接已复制，可以分享给团队成员'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '❌ 分享失败'
    })
  }
}

const bookmarkLog = (log) => {
  // 这里可以实现书签功能
  appStore.addNotification({
    type: 'success',
    message: '⭐ 日志已添加到书签'
  })
}

const clearFilters = () => {
  logStore.setSearchQuery('')
  logStore.setLogLevel('all')
  logStore.setSelectedSources([])
}

const refreshData = () => {
  appStore.addNotification({
    type: 'info',
    message: '🔄 正在刷新日志数据...'
  })
}
</script>

<style scoped>
mark {
  padding: 0 2px;
  border-radius: 2px;
}
</style>
