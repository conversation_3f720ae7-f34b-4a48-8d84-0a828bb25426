<template>
  <div class="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
    <!-- 顶部工具栏 -->
    <div class="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <div class="flex items-center justify-between">
        <!-- 左侧：标题和搜索 -->
        <div class="flex items-center space-x-6 flex-1">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <component :is="FileText" class="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 class="text-lg font-semibold text-gray-900 dark:text-white">日志查看器</h1>
            </div>
          </div>

          <!-- 搜索框 -->
          <div class="flex-1 max-w-2xl">
            <SearchBar />
          </div>
        </div>

        <!-- 右侧：控制按钮 -->
        <div class="flex items-center space-x-4">
          <!-- 实时状态 -->
          <div class="flex items-center space-x-2 px-3 py-1.5 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-sm font-medium text-green-700 dark:text-green-300">实时</span>
          </div>

          <!-- 视图切换 -->
          <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              @click="viewMode = 'table'"
              :class="[
                'px-3 py-1.5 text-sm font-medium rounded-md transition-colors',
                viewMode === 'table'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              ]"
            >
              <component :is="Table" class="w-4 h-4" />
            </button>
            <button
              @click="viewMode = 'terminal'"
              :class="[
                'px-3 py-1.5 text-sm font-medium rounded-md transition-colors',
                viewMode === 'terminal'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              ]"
            >
              <component :is="Terminal" class="w-4 h-4" />
            </button>
          </div>

          <!-- 操作按钮 -->
          <button
            @click="toggleFilters"
            :class="[
              'btn-secondary',
              showFilters ? 'bg-blue-50 text-blue-700 border-blue-300 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-700' : ''
            ]"
          >
            <component :is="Filter" class="w-4 h-4 mr-2" />
            过滤器
          </button>

          <button
            @click="showAdvancedSearch = true"
            class="btn-secondary"
          >
            <component :is="Search" class="w-4 h-4 mr-2" />
            高级搜索
          </button>

          <button
            @click="refreshLogs"
            :disabled="isRefreshing"
            class="btn-secondary"
          >
            <component :is="RefreshCw" :class="['w-4 h-4 mr-2', isRefreshing ? 'animate-spin' : '']" />
            刷新
          </button>

          <button
            @click="showExportModal = true"
            class="btn-primary"
          >
            <component :is="Download" class="w-4 h-4 mr-2" />
            导出
          </button>

          <button
            @click="showKeyboardShortcuts = true"
            class="btn-secondary"
            title="键盘快捷键 (?)"
          >
            <component :is="HelpCircle" class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- 过滤器面板 -->
    <div v-if="showFilters" class="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <div class="flex items-center space-x-6">
        <!-- 快速过滤器 -->
        <div class="flex items-center space-x-3">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">快速过滤:</span>
          <button
            v-for="filter in quickFilters"
            :key="filter.key"
            @click="applyQuickFilter(filter)"
            :class="[
              'px-3 py-1.5 text-sm font-medium rounded-lg transition-colors',
              isFilterActive(filter)
                ? 'bg-blue-100 text-blue-700 border border-blue-300 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-700'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            ]"
          >
            {{ filter.label }}
          </button>
        </div>

        <!-- 自定义过滤器 -->
        <div class="flex items-center space-x-3">
          <select
            v-model="logStore.selectedLogLevel"
            class="input w-28"
          >
            <option value="all">全部级别</option>
            <option value="error">错误</option>
            <option value="warn">警告</option>
            <option value="info">信息</option>
            <option value="debug">调试</option>
            <option value="trace">追踪</option>
          </select>

          <select
            v-model="logStore.selectedTimeRange"
            class="input w-32"
          >
            <option value="15m">最近15分钟</option>
            <option value="1h">最近1小时</option>
            <option value="6h">最近6小时</option>
            <option value="24h">最近24小时</option>
            <option value="7d">最近7天</option>
          </select>

          <SourceFilter />
        </div>

        <!-- 清除过滤器 -->
        <button
          v-if="hasActiveFilters"
          @click="clearFilters"
          class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          清除全部
        </button>
      </div>

      <!-- 活跃过滤器标签 -->
      <div v-if="hasActiveFilters" class="mt-3 flex flex-wrap gap-2">
        <span
          v-if="logStore.searchQuery"
          class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
        >
          搜索: "{{ logStore.searchQuery }}"
          <button
            @click="logStore.setSearchQuery('')"
            class="ml-1 text-blue-600 hover:text-blue-800"
          >
            <component :is="X" class="w-3 h-3" />
          </button>
        </span>
        <span
          v-if="logStore.selectedLogLevel !== 'all'"
          class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
        >
          级别: {{ logStore.selectedLogLevel }}
          <button
            @click="logStore.setLogLevel('all')"
            class="ml-1 text-green-600 hover:text-green-800"
          >
            <component :is="X" class="w-3 h-3" />
          </button>
        </span>
        <span
          v-if="logStore.selectedSources.length > 0"
          class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
        >
          来源: {{ logStore.selectedSources.length }}
          <button
            @click="logStore.setSelectedSources([])"
            class="ml-1 text-purple-600 hover:text-purple-800"
          >
            <component :is="X" class="w-3 h-3" />
          </button>
        </span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 侧边栏 -->
      <div v-if="showStats" class="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
        <!-- 侧边栏标签 -->
        <div class="flex border-b border-gray-200 dark:border-gray-700">
          <button
            v-for="tab in sidebarTabs"
            :key="tab.key"
            @click="activeSidebarTab = tab.key"
            :class="[
              'flex-1 px-4 py-3 text-sm font-medium transition-colors',
              activeSidebarTab === tab.key
                ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
            ]"
          >
            <component :is="tab.icon" class="w-4 h-4 mr-2 inline" />
            {{ tab.label }}
          </button>
        </div>

        <!-- 侧边栏内容 -->
        <div class="flex-1 overflow-hidden">
          <div v-if="activeSidebarTab === 'analytics'" class="h-full">
            <LogAnalytics />
          </div>
          <div v-else-if="activeSidebarTab === 'bookmarks'" class="h-full">
            <LogBookmarks />
          </div>
          <div v-else-if="activeSidebarTab === 'alerts'" class="h-full">
            <LogAlerts />
          </div>
        </div>
      </div>

      <!-- 日志内容区域 -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- 内容工具栏 -->
        <div class="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <span class="text-sm text-gray-600 dark:text-gray-400">
                显示 {{ getDisplayRange() }} 条，共 {{ logStore.filteredLogs.length.toLocaleString() }} 条
              </span>

              <!-- 级别快速过滤 -->
              <div class="flex items-center space-x-1">
                <button
                  v-for="level in ['error', 'warn', 'info']"
                  :key="level"
                  @click="toggleLevelFilter(level)"
                  :class="[
                    'px-2 py-1 text-xs font-medium rounded-md transition-colors',
                    logStore.selectedLogLevel === level
                      ? getLevelBadgeClass(level)
                      : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                  ]"
                >
                  {{ level.toUpperCase() }}
                </button>
              </div>
            </div>

            <div class="flex items-center space-x-3">
              <!-- 统计面板切换 -->
              <button
                @click="toggleStats"
                :class="[
                  'btn-secondary text-sm',
                  showStats ? 'bg-blue-50 text-blue-700 border-blue-300 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-700' : ''
                ]"
              >
                <component :is="BarChart3" class="w-4 h-4 mr-1" />
                统计
              </button>

              <!-- 每页显示数量 -->
              <select
                v-model="logStore.pageSize"
                class="input w-20 text-sm"
              >
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
                <option :value="200">200</option>
              </select>

              <!-- 自动刷新 -->
              <button
                @click="toggleAutoRefresh"
                :class="[
                  'btn-secondary text-sm',
                  autoRefresh ? 'bg-green-50 text-green-700 border-green-300 dark:bg-green-900/20 dark:text-green-400 dark:border-green-700' : ''
                ]"
              >
                <component :is="autoRefresh ? Activity : Clock" class="w-4 h-4 mr-1" />
                {{ autoRefresh ? '自动' : '手动' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 日志内容 -->
        <div class="flex-1 overflow-hidden relative">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="absolute inset-0 bg-white/50 dark:bg-gray-800/50 flex items-center justify-center z-10">
            <div class="flex items-center space-x-3">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              <span class="text-sm text-gray-600 dark:text-gray-400">加载中...</span>
            </div>
          </div>

          <!-- 视图内容 -->
          <div class="h-full">
            <div v-if="viewMode === 'table'" class="h-full">
              <LogList />
            </div>
            <div v-else-if="viewMode === 'terminal'" class="h-full">
              <VirtualTerminal />
            </div>
          </div>
        </div>

        <!-- 底部分页 -->
        <div v-if="logStore.totalPages > 1" class="flex-shrink-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-6 py-3">
          <Pagination />
        </div>
      </div>
    </div>

    <!-- 键盘快捷键帮助 -->
    <KeyboardShortcuts
      v-if="showKeyboardShortcuts"
      @close="showKeyboardShortcuts = false"
    />

    <!-- 导出模态框 -->
    <ExportModal
      v-if="showExportModal"
      :logs="logStore.logs"
      :filtered-logs="logStore.filteredLogs"
      :current-filters="currentFilters"
      @close="showExportModal = false"
    />

    <!-- 高级搜索模态框 -->
    <AdvancedSearchModal
      v-if="showAdvancedSearch"
      @close="showAdvancedSearch = false"
      @search="handleAdvancedSearch"
    />
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref, nextTick } from 'vue'
import { useLogStore } from '@/stores/logStore'
import { useAppStore } from '@/stores/appStore'
import { generateRealtimeLog } from '@/utils/mockData'
import {
  X,
  Download,
  Table,
  Terminal,
  Filter,
  FileText,
  RefreshCw,
  Activity,
  Clock,
  BarChart3,
  HelpCircle,
  Search,
  Bookmark,
  Bell
} from 'lucide-vue-next'

import SearchBar from '@/components/SearchBar.vue'
import SourceFilter from '@/components/SourceFilter.vue'
import LogList from '@/components/LogList.vue'
import Pagination from '@/components/Pagination.vue'
import AdvancedFilter from '@/components/AdvancedFilter.vue'
import VirtualTerminal from '@/components/VirtualTerminal.vue'
import StatsCard from '@/components/StatsCard.vue'
import KeyboardShortcuts from '@/components/KeyboardShortcuts.vue'
import ExportModal from '@/components/ExportModal.vue'
import AdvancedSearchModal from '@/components/AdvancedSearchModal.vue'
import LogAnalytics from '@/components/LogAnalytics.vue'
import LogBookmarks from '@/components/LogBookmarks.vue'
import LogAlerts from '@/components/LogAlerts.vue'

const logStore = useLogStore()
const appStore = useAppStore()

// 组件状态
const viewMode = ref('table') // 'table' | 'terminal'
const isLoading = ref(false)
const isRefreshing = ref(false)
const autoRefresh = ref(true)
const showFilters = ref(false)
const showStats = ref(true)
const showKeyboardShortcuts = ref(false)
const showExportModal = ref(false)
const showAdvancedSearch = ref(false)
const activeSidebarTab = ref('analytics')

// 快速过滤器
const quickFilters = ref([
  { key: 'errors', label: '仅错误', level: 'error' },
  { key: 'warnings', label: '警告+错误', level: 'warn' },
  { key: 'recent', label: '最近1小时', timeRange: '1h' },
  { key: 'today', label: '今天', timeRange: '24h' }
])

// 侧边栏标签配置
const sidebarTabs = ref([
  { key: 'analytics', label: '分析', icon: 'BarChart3' },
  { key: 'bookmarks', label: '书签', icon: 'Bookmark' },
  { key: 'alerts', label: '告警', icon: 'Bell' }
])

let realtimeInterval = null

// 计算属性
const hasActiveFilters = computed(() => {
  return logStore.searchQuery ||
         logStore.selectedLogLevel !== 'all' ||
         logStore.selectedSources.length > 0 ||
         logStore.selectedTimeRange !== '24h'
})

const currentFilters = computed(() => {
  return {
    searchQuery: logStore.searchQuery,
    logLevel: logStore.selectedLogLevel,
    sources: logStore.selectedSources,
    timeRange: logStore.selectedTimeRange
  }
})

// 辅助方法
const getLevelDisplayName = (level) => {
  const names = {
    error: '🔴 错误',
    warn: '🟡 警告',
    info: '🔵 信息',
    debug: '⚪ 调试',
    trace: '🟣 追踪'
  }
  return names[level] || level
}

const getTimeRangeDisplayName = (range) => {
  const names = {
    '1h': '最近1小时',
    '6h': '最近6小时',
    '24h': '最近24小时',
    '7d': '最近7天'
  }
  return names[range] || range
}

const getErrorRate = () => {
  const total = logStore.logs.length
  if (total === 0) return 0
  return Math.round((logStore.logLevelCounts.error / total) * 100)
}

const getWarnRate = () => {
  const total = logStore.logs.length
  if (total === 0) return 0
  return Math.round((logStore.logLevelCounts.warn / total) * 100)
}

const getDisplayRange = () => {
  const start = (logStore.currentPage - 1) * logStore.pageSize + 1
  const end = Math.min(logStore.currentPage * logStore.pageSize, logStore.filteredLogs.length)
  return `${start}-${end}`
}

// 操作方法
const clearFilters = () => {
  logStore.setSearchQuery('')
  logStore.setLogLevel('all')
  logStore.setSelectedSources([])
  logStore.setTimeRange('24h')
}

const refreshLogs = async () => {
  isRefreshing.value = true
  try {
    // 模拟刷新延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 这里可以添加实际的刷新逻辑
    appStore.addNotification({
      type: 'success',
      message: '🔄 日志已刷新'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '❌ 刷新失败: ' + error.message
    })
  } finally {
    isRefreshing.value = false
  }
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  appStore.addNotification({
    type: 'info',
    message: autoRefresh.value ? '已开启自动刷新' : '已关闭自动刷新'
  })
}

const toggleFilters = () => {
  showFilters.value = !showFilters.value
}

const toggleStats = () => {
  showStats.value = !showStats.value
}

const applyQuickFilter = (filter) => {
  if (filter.level) {
    logStore.setLogLevel(filter.level)
  }
  if (filter.timeRange) {
    logStore.setTimeRange(filter.timeRange)
  }
}

const isFilterActive = (filter) => {
  if (filter.level && logStore.selectedLogLevel === filter.level) {
    return true
  }
  if (filter.timeRange && logStore.selectedTimeRange === filter.timeRange) {
    return true
  }
  return false
}

const toggleLevelFilter = (level) => {
  if (logStore.selectedLogLevel === level) {
    logStore.setLogLevel('all')
  } else {
    logStore.setLogLevel(level)
  }
}

const getLevelColor = (level) => {
  const colors = {
    error: 'bg-red-500',
    warn: 'bg-yellow-500',
    info: 'bg-blue-500',
    debug: 'bg-gray-500',
    trace: 'bg-purple-500'
  }
  return colors[level] || 'bg-gray-500'
}

const getLevelBadgeClass = (level) => {
  const classes = {
    error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    warn: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    info: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    debug: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    trace: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
  }
  return classes[level] || classes.info
}

const exportLogs = () => {
  const logs = logStore.filteredLogs
  const csvContent = [
    'Timestamp,Level,Source,Service,Message',
    ...logs.map(log => 
      `"${log.timestamp}","${log.level}","${log.source}","${log.service}","${log.message.replace(/"/g, '""')}"`
    )
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `logs-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
  
  appStore.addNotification({
    type: 'success',
    message: `已导出 ${logs.length} 条日志记录`
  })
}

const handleAdvancedSearch = (searchData) => {
  // 应用高级搜索条件
  if (searchData.keywords) {
    logStore.setSearchQuery(searchData.keywords)
  }
  if (searchData.level) {
    logStore.setLogLevel(searchData.level)
  }
  if (searchData.source) {
    logStore.setSelectedSources([searchData.source])
  }
  if (searchData.timeType === 'relative' && searchData.relativeTime) {
    logStore.setTimeRange(searchData.relativeTime)
  }

  appStore.addNotification({
    type: 'success',
    message: '高级搜索条件已应用'
  })
}

const startRealtimeUpdates = () => {
  if (realtimeInterval) return
  
  realtimeInterval = setInterval(() => {
    if (logStore.isRealTimeMode) {
      // Simulate receiving new logs
      const newLog = generateRealtimeLog()
      logStore.addLog(newLog)
    }
  }, 2000) // Add new log every 2 seconds
}

const stopRealtimeUpdates = () => {
  if (realtimeInterval) {
    clearInterval(realtimeInterval)
    realtimeInterval = null
  }
}

// 键盘快捷键处理
const handleKeydown = (event) => {
  // Ctrl/Cmd + K: 聚焦搜索框
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    const searchInput = document.querySelector('input[type="text"]')
    if (searchInput) {
      searchInput.focus()
    }
  }

  // Ctrl/Cmd + F: 显示过滤器
  if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
    event.preventDefault()
    showFilters.value = !showFilters.value
  }

  // Ctrl/Cmd + R: 刷新日志
  if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
    event.preventDefault()
    refreshLogs()
  }

  // Ctrl/Cmd + S: 切换统计面板
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault()
    showStats.value = !showStats.value
  }

  // Ctrl/Cmd + T: 切换视图模式
  if ((event.ctrlKey || event.metaKey) && event.key === 't') {
    event.preventDefault()
    viewMode.value = viewMode.value === 'table' ? 'terminal' : 'table'
  }

  // ?: 显示快捷键帮助
  if (event.key === '?' && !event.ctrlKey && !event.metaKey) {
    event.preventDefault()
    showKeyboardShortcuts.value = true
  }

  // Escape: 清除搜索或关闭面板
  if (event.key === 'Escape') {
    if (showKeyboardShortcuts.value) {
      showKeyboardShortcuts.value = false
    } else if (logStore.searchQuery) {
      logStore.setSearchQuery('')
    } else if (showFilters.value) {
      showFilters.value = false
    }
  }

  // 数字键快速过滤
  if (event.key >= '1' && event.key <= '4' && (event.ctrlKey || event.metaKey)) {
    event.preventDefault()
    const filterIndex = parseInt(event.key) - 1
    if (quickFilters.value[filterIndex]) {
      applyQuickFilter(quickFilters.value[filterIndex])
    }
  }
}

onMounted(() => {
  logStore.fetchLogs()
  startRealtimeUpdates()

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  stopRealtimeUpdates()

  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)
})
</script>
