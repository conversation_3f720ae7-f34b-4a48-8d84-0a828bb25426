<template>
  <div class="space-y-6">
    <!-- 页面头部和控制区 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">日志查看器</h2>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">实时监控和分析系统日志</p>
        </div>

        <!-- 右侧控制区 -->
        <div class="flex items-center space-x-4">
          <!-- 实时状态指示器 -->
          <div class="flex items-center space-x-2 px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-sm font-medium text-green-700 dark:text-green-300">实时监控</span>
          </div>

          <!-- 视图模式切换 -->
          <div class="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            <button
              @click="viewMode = 'table'"
              :class="[
                'px-3 py-2 text-sm font-medium rounded-md transition-colors',
                viewMode === 'table'
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              ]"
            >
              <component :is="Table" class="w-4 h-4 mr-2 inline" />
              表格视图
            </button>
            <button
              @click="viewMode = 'terminal'"
              :class="[
                'px-3 py-2 text-sm font-medium rounded-md transition-colors',
                viewMode === 'terminal'
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              ]"
            >
              <component :is="Terminal" class="w-4 h-4 mr-2 inline" />
              终端视图
            </button>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center space-x-2">
            <button
              @click="refreshLogs"
              :disabled="isRefreshing"
              class="btn-secondary"
            >
              <component :is="RefreshCw" :class="['w-4 h-4 mr-2', isRefreshing ? 'animate-spin' : '']" />
              刷新
            </button>
            <button
              @click="exportLogs"
              class="btn-primary"
            >
              <component :is="Download" class="w-4 h-4 mr-2" />
              导出
            </button>
          </div>
        </div>
      </div>

      <!-- 搜索和过滤器区域 -->
      <div class="space-y-4">
        <!-- 搜索栏 -->
        <SearchBar />

        <!-- 过滤器控制栏 -->
        <div class="flex flex-wrap items-center gap-4">
          <!-- 日志级别过滤器 -->
          <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">级别:</label>
            <select
              v-model="logStore.selectedLogLevel"
              class="input w-32"
            >
              <option value="all">全部</option>
              <option value="error">错误</option>
              <option value="warn">警告</option>
              <option value="info">信息</option>
              <option value="debug">调试</option>
              <option value="trace">追踪</option>
            </select>
          </div>

          <!-- 时间范围过滤器 -->
          <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">时间:</label>
            <select
              v-model="logStore.selectedTimeRange"
              class="input w-32"
            >
              <option value="1h">最近1小时</option>
              <option value="6h">最近6小时</option>
              <option value="24h">最近24小时</option>
              <option value="7d">最近7天</option>
            </select>
          </div>

          <!-- 来源过滤器 -->
          <SourceFilter />

          <!-- 清除过滤器 -->
          <button
            v-if="hasActiveFilters"
            @click="clearFilters"
            class="btn-secondary"
          >
            <component :is="X" class="w-4 h-4 mr-2" />
            清除过滤器
          </button>
        </div>

        <!-- 活跃过滤器摘要 -->
        <div v-if="hasActiveFilters" class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between mb-3">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">活跃过滤器</span>
            <button
              @click="clearFilters"
              class="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              清除全部
            </button>
          </div>
          <div class="flex flex-wrap gap-2">
            <span
              v-if="logStore.searchQuery"
              class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              搜索: "{{ logStore.searchQuery }}"
              <button
                @click="logStore.setSearchQuery('')"
                class="ml-1 text-blue-600 hover:text-blue-800"
              >
                <component :is="X" class="w-3 h-3" />
              </button>
            </span>
            <span
              v-if="logStore.selectedLogLevel !== 'all'"
              class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              级别: {{ logStore.selectedLogLevel }}
              <button
                @click="logStore.setLogLevel('all')"
                class="ml-1 text-green-600 hover:text-green-800"
              >
                <component :is="X" class="w-3 h-3" />
              </button>
            </span>
            <span
              v-if="logStore.selectedSources.length > 0"
              class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
            >
              来源: {{ logStore.selectedSources.length }}
              <button
                @click="logStore.setSelectedSources([])"
                class="ml-1 text-purple-600 hover:text-purple-800"
              >
                <component :is="X" class="w-3 h-3" />
              </button>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatsCard
        title="过滤结果"
        :value="logStore.filteredLogs.length.toLocaleString()"
        icon="FileText"
        color="blue"
        :description="`共 ${logStore.logs.length.toLocaleString()} 条日志`"
      />
      <StatsCard
        title="错误日志"
        :value="logStore.logLevelCounts.error.toLocaleString()"
        icon="AlertTriangle"
        color="red"
        :description="`${getErrorRate()}% 错误率`"
        :trend="{ value: 5, direction: 'down' }"
      />
      <StatsCard
        title="警告日志"
        :value="logStore.logLevelCounts.warn.toLocaleString()"
        icon="AlertCircle"
        color="yellow"
        :description="`${getWarnRate()}% 警告率`"
        :trend="{ value: 8, direction: 'up' }"
      />
      <StatsCard
        title="实时监控"
        :value="logStore.isRealTimeMode ? '运行中' : '已暂停'"
        icon="Activity"
        color="green"
        :description="logStore.isRealTimeMode ? '正在监听新日志' : '点击开始监听'"
      />
    </div>

    <!-- 高级过滤器 -->
    <AdvancedFilter />

    <!-- 日志内容区域 -->
    <div class="card overflow-hidden">
      <!-- 工具栏 -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">日志条目</h3>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              显示 {{ getDisplayRange() }} 条，共 {{ logStore.filteredLogs.length.toLocaleString() }} 条
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <!-- 每页显示数量 -->
            <div class="flex items-center space-x-2">
              <label class="text-sm text-gray-600 dark:text-gray-400">每页:</label>
              <select
                v-model="logStore.pageSize"
                class="input w-20"
              >
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
                <option :value="200">200</option>
                <option :value="500">500</option>
              </select>
            </div>

            <!-- 自动刷新切换 -->
            <button
              @click="toggleAutoRefresh"
              :class="[
                'btn-secondary',
                autoRefresh ? 'bg-green-100 text-green-700 border-green-300 dark:bg-green-900/20 dark:text-green-400 dark:border-green-700' : ''
              ]"
            >
              <component :is="autoRefresh ? Activity : Clock" class="w-4 h-4 mr-2" />
              {{ autoRefresh ? '自动刷新' : '手动刷新' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 日志内容 -->
      <div class="relative">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="absolute inset-0 bg-white/50 dark:bg-gray-800/50 flex items-center justify-center z-10">
          <div class="flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">加载中...</span>
          </div>
        </div>

        <!-- 视图切换内容 -->
        <div v-if="viewMode === 'table'">
          <LogList />
        </div>
        <div v-else-if="viewMode === 'terminal'" class="h-96">
          <VirtualTerminal />
        </div>
      </div>

      <!-- 分页器 -->
      <div v-if="logStore.totalPages > 1" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <Pagination />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useLogStore } from '@/stores/logStore'
import { useAppStore } from '@/stores/appStore'
import { generateRealtimeLog } from '@/utils/mockData'
import {
  X,
  Download,
  ChevronDown,
  Table,
  Terminal,
  Filter,
  Search,
  AlertCircle,
  Database,
  Clock,
  FileText,
  AlertTriangle,
  Activity,
  RefreshCw,
  Maximize2,
  Minimize2
} from 'lucide-vue-next'

import SearchBar from '@/components/SearchBar.vue'
import SourceFilter from '@/components/SourceFilter.vue'
import LogList from '@/components/LogList.vue'
import Pagination from '@/components/Pagination.vue'
import AdvancedFilter from '@/components/AdvancedFilter.vue'
import VirtualTerminal from '@/components/VirtualTerminal.vue'
import StatsCard from '@/components/StatsCard.vue'

const logStore = useLogStore()
const appStore = useAppStore()

// 组件状态
const viewMode = ref('table') // 'table' | 'terminal'
const isLoading = ref(false)
const isRefreshing = ref(false)
const autoRefresh = ref(true)
const isFullscreen = ref(false)

let realtimeInterval = null

// 计算属性
const hasActiveFilters = computed(() => {
  return logStore.searchQuery ||
         logStore.selectedLogLevel !== 'all' ||
         logStore.selectedSources.length > 0 ||
         logStore.selectedTimeRange !== '24h'
})

// 辅助方法
const getLevelDisplayName = (level) => {
  const names = {
    error: '🔴 错误',
    warn: '🟡 警告',
    info: '🔵 信息',
    debug: '⚪ 调试',
    trace: '🟣 追踪'
  }
  return names[level] || level
}

const getTimeRangeDisplayName = (range) => {
  const names = {
    '1h': '最近1小时',
    '6h': '最近6小时',
    '24h': '最近24小时',
    '7d': '最近7天'
  }
  return names[range] || range
}

const getErrorRate = () => {
  const total = logStore.logs.length
  if (total === 0) return 0
  return Math.round((logStore.logLevelCounts.error / total) * 100)
}

const getWarnRate = () => {
  const total = logStore.logs.length
  if (total === 0) return 0
  return Math.round((logStore.logLevelCounts.warn / total) * 100)
}

const getDisplayRange = () => {
  const start = (logStore.currentPage - 1) * logStore.pageSize + 1
  const end = Math.min(logStore.currentPage * logStore.pageSize, logStore.filteredLogs.length)
  return `${start}-${end}`
}

// 操作方法
const clearFilters = () => {
  logStore.setSearchQuery('')
  logStore.setLogLevel('all')
  logStore.setSelectedSources([])
  logStore.setTimeRange('24h')
}

const refreshLogs = async () => {
  isRefreshing.value = true
  try {
    // 模拟刷新延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 这里可以添加实际的刷新逻辑
    appStore.addNotification({
      type: 'success',
      message: '🔄 日志已刷新'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '❌ 刷新失败: ' + error.message
    })
  } finally {
    isRefreshing.value = false
  }
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  appStore.addNotification({
    type: 'info',
    message: autoRefresh.value ? '✅ 已开启自动刷新' : '⏸️ 已关闭自动刷新'
  })
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value

  if (isFullscreen.value) {
    // 进入全屏模式
    document.documentElement.requestFullscreen?.()
    appStore.addNotification({
      type: 'info',
      message: '🖥️ 已进入全屏模式'
    })
  } else {
    // 退出全屏模式
    document.exitFullscreen?.()
    appStore.addNotification({
      type: 'info',
      message: '📱 已退出全屏模式'
    })
  }
}

const exportLogs = () => {
  const logs = logStore.filteredLogs
  const csvContent = [
    'Timestamp,Level,Source,Service,Message',
    ...logs.map(log => 
      `"${log.timestamp}","${log.level}","${log.source}","${log.service}","${log.message.replace(/"/g, '""')}"`
    )
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `logs-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
  
  appStore.addNotification({
    type: 'success',
    message: `已导出 ${logs.length} 条日志记录`
  })
}

const startRealtimeUpdates = () => {
  if (realtimeInterval) return
  
  realtimeInterval = setInterval(() => {
    if (logStore.isRealTimeMode) {
      // Simulate receiving new logs
      const newLog = generateRealtimeLog()
      logStore.addLog(newLog)
    }
  }, 2000) // Add new log every 2 seconds
}

const stopRealtimeUpdates = () => {
  if (realtimeInterval) {
    clearInterval(realtimeInterval)
    realtimeInterval = null
  }
}

onMounted(() => {
  startRealtimeUpdates()
})

onUnmounted(() => {
  stopRealtimeUpdates()
})
</script>
