<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-slate-900 dark:to-gray-800">
    <!-- 页面头部 -->
    <div class="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50 sticky top-0 z-40">
      <div class="max-w-7xl mx-auto px-6 py-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
              <component :is="FileText" class="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 class="text-2xl font-bold text-gray-900 dark:text-white">日志查看器</h1>
              <p class="text-sm text-gray-500 dark:text-gray-400">实时监控和分析系统日志</p>
            </div>
          </div>

          <!-- 右侧操作区 -->
          <div class="flex items-center space-x-4">
            <!-- 实时状态 -->
            <div class="flex items-center space-x-2 px-4 py-2 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800">
              <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-sm font-medium text-green-700 dark:text-green-300">实时监控</span>
            </div>

            <!-- 视图切换 -->
            <div class="flex items-center bg-gray-100 dark:bg-gray-800 rounded-xl p-1 shadow-inner">
              <button
                @click="viewMode = 'table'"
                :class="[
                  'flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300',
                  viewMode === 'table'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-md transform scale-105'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-gray-700/50'
                ]"
              >
                <component :is="Table" class="w-4 h-4" />
                <span>表格</span>
              </button>
              <button
                @click="viewMode = 'terminal'"
                :class="[
                  'flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300',
                  viewMode === 'terminal'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-md transform scale-105'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-gray-700/50'
                ]"
              >
                <component :is="Terminal" class="w-4 h-4" />
                <span>终端</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-6 py-8 space-y-8">
      <!-- 搜索和过滤器区域 -->
      <div class="bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-white/20 dark:border-gray-700/30">
        <!-- 搜索栏 -->
        <div class="mb-8">
          <SearchBar />
        </div>

        <!-- 过滤器控制栏 -->
        <div class="flex flex-wrap items-center gap-4">
          <!-- 日志级别过滤器 -->
          <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">级别:</label>
            <select
              v-model="logStore.selectedLogLevel"
              class="appearance-none bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-300/50 dark:border-gray-600/50 rounded-xl px-4 py-2.5 pr-10 text-sm font-medium text-gray-700 dark:text-gray-200 hover:border-blue-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200/50 dark:focus:ring-blue-800/50 transition-all duration-300 shadow-sm"
            >
              <option value="all">全部级别</option>
              <option value="error">🔴 错误</option>
              <option value="warn">🟡 警告</option>
              <option value="info">🔵 信息</option>
              <option value="debug">⚪ 调试</option>
              <option value="trace">🟣 追踪</option>
            </select>
          </div>

          <!-- 时间范围过滤器 -->
          <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">时间:</label>
            <select
              v-model="logStore.selectedTimeRange"
              class="appearance-none bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-300/50 dark:border-gray-600/50 rounded-xl px-4 py-2.5 pr-10 text-sm font-medium text-gray-700 dark:text-gray-200 hover:border-blue-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200/50 dark:focus:ring-blue-800/50 transition-all duration-300 shadow-sm"
            >
              <option value="1h">最近1小时</option>
              <option value="6h">最近6小时</option>
              <option value="24h">最近24小时</option>
              <option value="7d">最近7天</option>
            </select>
          </div>

          <!-- 来源过滤器 -->
          <SourceFilter />

          <!-- 操作按钮组 -->
          <div class="flex items-center space-x-3 ml-auto">
            <!-- 刷新按钮 -->
            <button
              @click="refreshLogs"
              :disabled="isRefreshing"
              class="inline-flex items-center space-x-2 px-4 py-2.5 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white text-sm font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              <component :is="RefreshCw" :class="['w-4 h-4', isRefreshing ? 'animate-spin' : '']" />
              <span>刷新</span>
            </button>

            <!-- 导出按钮 -->
            <button
              @click="exportLogs"
              class="inline-flex items-center space-x-2 px-4 py-2.5 bg-green-500 hover:bg-green-600 text-white text-sm font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              <component :is="Download" class="w-4 h-4" />
              <span>导出</span>
            </button>

            <!-- 清除过滤器 -->
            <button
              v-if="hasActiveFilters"
              @click="clearFilters"
              class="inline-flex items-center space-x-2 px-4 py-2.5 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              <component :is="X" class="w-4 h-4" />
              <span>清除过滤器</span>
            </button>
          </div>
        </div>

        <!-- 活跃过滤器摘要 -->
        <div v-if="hasActiveFilters" class="mt-6 p-6 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200/50 dark:border-blue-700/30">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <component :is="Filter" class="w-4 h-4 text-white" />
              </div>
              <span class="text-lg font-semibold text-gray-900 dark:text-white">活跃过滤器</span>
            </div>
          </div>

          <div class="flex flex-wrap items-center gap-3">
            <!-- 搜索过滤器 -->
            <div
              v-if="logStore.searchQuery"
              class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200 border border-blue-200 dark:border-blue-700 shadow-sm"
            >
              <component :is="Search" class="w-4 h-4 mr-2" />
              搜索: "{{ logStore.searchQuery }}"
              <button
                @click="logStore.setSearchQuery('')"
                class="ml-3 p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800 rounded-lg transition-all duration-200"
              >
                <component :is="X" class="w-3 h-3" />
              </button>
            </div>

            <!-- 级别过滤器 -->
            <div
              v-if="logStore.selectedLogLevel !== 'all'"
              class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200 border border-green-200 dark:border-green-700 shadow-sm"
            >
              <component :is="AlertCircle" class="w-4 h-4 mr-2" />
              级别: {{ getLevelDisplayName(logStore.selectedLogLevel) }}
              <button
                @click="logStore.setLogLevel('all')"
                class="ml-3 p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200 hover:bg-green-200 dark:hover:bg-green-800 rounded-lg transition-all duration-200"
              >
                <component :is="X" class="w-3 h-3" />
              </button>
            </div>

            <!-- 来源过滤器 -->
            <div
              v-if="logStore.selectedSources.length > 0"
              class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200 border border-purple-200 dark:border-purple-700 shadow-sm"
            >
              <component :is="Database" class="w-4 h-4 mr-2" />
              来源: {{ logStore.selectedSources.length }} 个
              <button
                @click="logStore.setSelectedSources([])"
                class="ml-3 p-1 text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-200 hover:bg-purple-200 dark:hover:bg-purple-800 rounded-lg transition-all duration-200"
              >
                <component :is="X" class="w-3 h-3" />
              </button>
            </div>

            <!-- 时间范围过滤器 -->
            <div
              v-if="logStore.selectedTimeRange !== '24h'"
              class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200 border border-orange-200 dark:border-orange-700 shadow-sm"
            >
              <component :is="Clock" class="w-4 h-4 mr-2" />
              时间: {{ getTimeRangeDisplayName(logStore.selectedTimeRange) }}
              <button
                @click="logStore.setTimeRange('24h')"
                class="ml-3 p-1 text-orange-600 hover:text-orange-800 dark:text-orange-400 dark:hover:text-orange-200 hover:bg-orange-200 dark:hover:bg-orange-800 rounded-lg transition-all duration-200"
              >
                <component :is="X" class="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计概览 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- 过滤结果统计 -->
        <div class="group bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20 dark:border-gray-700/30 hover:shadow-2xl hover:scale-105 transition-all duration-300">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-2">
                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                <p class="text-sm font-semibold text-blue-600 dark:text-blue-400">过滤结果</p>
              </div>
              <p class="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                {{ logStore.filteredLogs.length.toLocaleString() }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                共 {{ logStore.logs.length.toLocaleString() }} 条日志
              </p>
            </div>
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
              <component :is="FileText" class="w-8 h-8 text-white" />
            </div>
          </div>
        </div>

        <!-- 错误统计 -->
        <div class="group bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20 dark:border-gray-700/30 hover:shadow-2xl hover:scale-105 transition-all duration-300">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-2">
                <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <p class="text-sm font-semibold text-red-600 dark:text-red-400">错误日志</p>
              </div>
              <p class="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                {{ logStore.logLevelCounts.error.toLocaleString() }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ getErrorRate() }}% 错误率
              </p>
            </div>
            <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
              <component :is="AlertTriangle" class="w-8 h-8 text-white" />
            </div>
          </div>
        </div>

        <!-- 警告统计 -->
        <div class="group bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20 dark:border-gray-700/30 hover:shadow-2xl hover:scale-105 transition-all duration-300">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-2">
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <p class="text-sm font-semibold text-yellow-600 dark:text-yellow-400">警告日志</p>
              </div>
              <p class="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                {{ logStore.logLevelCounts.warn.toLocaleString() }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ getWarnRate() }}% 警告率
              </p>
            </div>
            <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
              <component :is="AlertCircle" class="w-8 h-8 text-white" />
            </div>
          </div>
        </div>

        <!-- 实时状态 -->
        <div class="group bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20 dark:border-gray-700/30 hover:shadow-2xl hover:scale-105 transition-all duration-300">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-2">
                <div
                  class="w-3 h-3 rounded-full"
                  :class="logStore.isRealTimeMode ? 'bg-green-500 animate-pulse' : 'bg-gray-400'"
                ></div>
                <p class="text-sm font-semibold text-green-600 dark:text-green-400">实时监控</p>
              </div>
              <p class="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                {{ logStore.isRealTimeMode ? '运行中' : '已暂停' }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ logStore.isRealTimeMode ? '正在监听新日志' : '点击开始监听' }}
              </p>
            </div>
            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
              <component :is="Activity" class="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      <!-- 高级过滤器 -->
      <AdvancedFilter />

      <!-- 日志内容区域 -->
      <div class="bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-gray-700/30 overflow-hidden">
        <!-- 工具栏 -->
        <div class="bg-gradient-to-r from-gray-50/80 to-gray-100/80 dark:from-gray-800/80 dark:to-gray-900/80 backdrop-blur-sm px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <component :is="FileText" class="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-900 dark:text-white">日志条目</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    显示 {{ getDisplayRange() }} 条，共 {{ logStore.filteredLogs.length.toLocaleString() }} 条
                  </p>
                </div>
              </div>
            </div>

            <div class="flex items-center space-x-4">
              <!-- 每页显示数量 -->
              <div class="flex items-center space-x-3">
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">每页显示:</label>
                <select
                  v-model="logStore.pageSize"
                  class="appearance-none bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-300/50 dark:border-gray-600/50 rounded-xl px-4 py-2 pr-10 text-sm font-medium text-gray-700 dark:text-gray-200 hover:border-blue-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200/50 dark:focus:ring-blue-800/50 transition-all duration-300 shadow-sm"
                >
                  <option :value="25">25 条</option>
                  <option :value="50">50 条</option>
                  <option :value="100">100 条</option>
                  <option :value="200">200 条</option>
                  <option :value="500">500 条</option>
                </select>
              </div>

              <!-- 操作按钮组 -->
              <div class="flex items-center space-x-2">
                <!-- 自动刷新切换 -->
                <button
                  @click="toggleAutoRefresh"
                  :class="[
                    'inline-flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 shadow-sm',
                    autoRefresh
                      ? 'bg-green-500 hover:bg-green-600 text-white shadow-green-200 dark:shadow-green-900/50'
                      : 'bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300'
                  ]"
                >
                  <component :is="autoRefresh ? Activity : Clock" class="w-4 h-4" />
                  <span>{{ autoRefresh ? '自动刷新' : '手动刷新' }}</span>
                </button>

                <!-- 全屏切换 -->
                <button
                  @click="toggleFullscreen"
                  class="inline-flex items-center space-x-2 px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white text-sm font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                >
                  <component :is="isFullscreen ? Minimize2 : Maximize2" class="w-4 h-4" />
                  <span>{{ isFullscreen ? '退出全屏' : '全屏查看' }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 日志内容 -->
        <div class="relative min-h-[600px]">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="absolute inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-20">
            <div class="flex flex-col items-center space-y-4">
              <div class="relative">
                <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-500"></div>
                <div class="absolute inset-0 rounded-full h-12 w-12 border-4 border-transparent border-t-blue-300 animate-ping"></div>
              </div>
              <div class="text-center">
                <p class="text-lg font-medium text-gray-900 dark:text-white">加载中...</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">正在获取最新日志数据</p>
              </div>
            </div>
          </div>

          <!-- 视图切换内容 -->
          <div v-if="viewMode === 'table'" class="transition-all duration-500">
            <LogList />
          </div>
          <div v-else-if="viewMode === 'terminal'" class="h-[600px] transition-all duration-500">
            <VirtualTerminal />
          </div>
        </div>

        <!-- 分页器 -->
        <div v-if="logStore.totalPages > 1" class="bg-gradient-to-r from-gray-50/80 to-gray-100/80 dark:from-gray-800/80 dark:to-gray-900/80 backdrop-blur-sm px-8 py-6 border-t border-gray-200/50 dark:border-gray-700/50">
          <Pagination />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useLogStore } from '@/stores/logStore'
import { useAppStore } from '@/stores/appStore'
import { generateRealtimeLog } from '@/utils/mockData'
import {
  X,
  Download,
  ChevronDown,
  Table,
  Terminal,
  Filter,
  Search,
  AlertCircle,
  Database,
  Clock,
  FileText,
  AlertTriangle,
  Activity,
  RefreshCw,
  Maximize2,
  Minimize2
} from 'lucide-vue-next'

import SearchBar from '@/components/SearchBar.vue'
import SourceFilter from '@/components/SourceFilter.vue'
import LogList from '@/components/LogList.vue'
import Pagination from '@/components/Pagination.vue'
import AdvancedFilter from '@/components/AdvancedFilter.vue'
import VirtualTerminal from '@/components/VirtualTerminal.vue'

const logStore = useLogStore()
const appStore = useAppStore()

// 组件状态
const viewMode = ref('table') // 'table' | 'terminal'
const isLoading = ref(false)
const isRefreshing = ref(false)
const autoRefresh = ref(true)
const isFullscreen = ref(false)

let realtimeInterval = null

// 计算属性
const hasActiveFilters = computed(() => {
  return logStore.searchQuery ||
         logStore.selectedLogLevel !== 'all' ||
         logStore.selectedSources.length > 0 ||
         logStore.selectedTimeRange !== '24h'
})

// 辅助方法
const getLevelDisplayName = (level) => {
  const names = {
    error: '🔴 错误',
    warn: '🟡 警告',
    info: '🔵 信息',
    debug: '⚪ 调试',
    trace: '🟣 追踪'
  }
  return names[level] || level
}

const getTimeRangeDisplayName = (range) => {
  const names = {
    '1h': '最近1小时',
    '6h': '最近6小时',
    '24h': '最近24小时',
    '7d': '最近7天'
  }
  return names[range] || range
}

const getErrorRate = () => {
  const total = logStore.logs.length
  if (total === 0) return 0
  return Math.round((logStore.logLevelCounts.error / total) * 100)
}

const getWarnRate = () => {
  const total = logStore.logs.length
  if (total === 0) return 0
  return Math.round((logStore.logLevelCounts.warn / total) * 100)
}

const getDisplayRange = () => {
  const start = (logStore.currentPage - 1) * logStore.pageSize + 1
  const end = Math.min(logStore.currentPage * logStore.pageSize, logStore.filteredLogs.length)
  return `${start}-${end}`
}

// 操作方法
const clearFilters = () => {
  logStore.setSearchQuery('')
  logStore.setLogLevel('all')
  logStore.setSelectedSources([])
  logStore.setTimeRange('24h')
}

const refreshLogs = async () => {
  isRefreshing.value = true
  try {
    // 模拟刷新延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 这里可以添加实际的刷新逻辑
    appStore.addNotification({
      type: 'success',
      message: '🔄 日志已刷新'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '❌ 刷新失败: ' + error.message
    })
  } finally {
    isRefreshing.value = false
  }
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  appStore.addNotification({
    type: 'info',
    message: autoRefresh.value ? '✅ 已开启自动刷新' : '⏸️ 已关闭自动刷新'
  })
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value

  if (isFullscreen.value) {
    // 进入全屏模式
    document.documentElement.requestFullscreen?.()
    appStore.addNotification({
      type: 'info',
      message: '🖥️ 已进入全屏模式'
    })
  } else {
    // 退出全屏模式
    document.exitFullscreen?.()
    appStore.addNotification({
      type: 'info',
      message: '📱 已退出全屏模式'
    })
  }
}

const exportLogs = () => {
  const logs = logStore.filteredLogs
  const csvContent = [
    'Timestamp,Level,Source,Service,Message',
    ...logs.map(log => 
      `"${log.timestamp}","${log.level}","${log.source}","${log.service}","${log.message.replace(/"/g, '""')}"`
    )
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `logs-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
  
  appStore.addNotification({
    type: 'success',
    message: `已导出 ${logs.length} 条日志记录`
  })
}

const startRealtimeUpdates = () => {
  if (realtimeInterval) return
  
  realtimeInterval = setInterval(() => {
    if (logStore.isRealTimeMode) {
      // Simulate receiving new logs
      const newLog = generateRealtimeLog()
      logStore.addLog(newLog)
    }
  }, 2000) // Add new log every 2 seconds
}

const stopRealtimeUpdates = () => {
  if (realtimeInterval) {
    clearInterval(realtimeInterval)
    realtimeInterval = null
  }
}

onMounted(() => {
  startRealtimeUpdates()
})

onUnmounted(() => {
  stopRealtimeUpdates()
})
</script>
