<template>
  <div class="space-y-6">
    <!-- 顶部工具栏 -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-xl p-6 shadow-sm border border-blue-100 dark:border-gray-700">
      <!-- 主要控制区域 -->
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-6">
        <!-- 搜索区域 -->
        <div class="flex-1 max-w-2xl">
          <div class="relative">
            <SearchBar />
            <!-- 搜索提示 -->
            <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
              💡 支持正则表达式、通配符和高级搜索语法
            </div>
          </div>
        </div>

        <!-- 快速过滤器 -->
        <div class="flex items-center space-x-3">
          <!-- 日志级别过滤器 -->
          <div class="relative">
            <select
              v-model="logStore.selectedLogLevel"
              class="appearance-none bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-gray-700 dark:text-gray-200 hover:border-blue-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all duration-200"
            >
              <option value="all">🔍 全部级别</option>
              <option value="error">🔴 错误</option>
              <option value="warn">🟡 警告</option>
              <option value="info">🔵 信息</option>
              <option value="debug">⚪ 调试</option>
              <option value="trace">🟣 追踪</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <component :is="ChevronDown" class="w-4 h-4 text-gray-400" />
            </div>
          </div>

          <!-- 时间范围过滤器 -->
          <div class="relative">
            <select
              v-model="logStore.selectedTimeRange"
              class="appearance-none bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-gray-700 dark:text-gray-200 hover:border-blue-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all duration-200"
            >
              <option value="1h">⏰ 最近1小时</option>
              <option value="6h">🕕 最近6小时</option>
              <option value="24h">📅 最近24小时</option>
              <option value="7d">📊 最近7天</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <component :is="ChevronDown" class="w-4 h-4 text-gray-400" />
            </div>
          </div>

          <!-- 来源过滤器 -->
          <SourceFilter />

          <!-- 视图切换 -->
          <div class="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              @click="viewMode = 'table'"
              :class="[
                'px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200',
                viewMode === 'table'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              ]"
            >
              <component :is="Table" class="w-4 h-4" />
            </button>
            <button
              @click="viewMode = 'terminal'"
              :class="[
                'px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200',
                viewMode === 'terminal'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              ]"
            >
              <component :is="Terminal" class="w-4 h-4" />
            </button>
          </div>

          <!-- 清除过滤器 -->
          <button
            @click="clearFilters"
            class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            :disabled="!hasActiveFilters"
          >
            <component :is="X" class="w-4 h-4 mr-2" />
            清除
          </button>
        </div>
      </div>

      <!-- 过滤器摘要 -->
      <div v-if="hasActiveFilters" class="mt-6 p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-blue-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <component :is="Filter" class="w-4 h-4 text-blue-500" />
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">活跃过滤器</span>
          </div>
          <button
            @click="clearFilters"
            class="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          >
            清除全部
          </button>
        </div>

        <div class="mt-3 flex flex-wrap items-center gap-2">
          <!-- 搜索过滤器 -->
          <div
            v-if="logStore.searchQuery"
            class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200 border border-blue-200 dark:border-blue-700"
          >
            <component :is="Search" class="w-3 h-3 mr-1.5" />
            搜索: "{{ logStore.searchQuery }}"
            <button
              @click="logStore.setSearchQuery('')"
              class="ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 transition-colors"
            >
              <component :is="X" class="w-3 h-3" />
            </button>
          </div>

          <!-- 级别过滤器 -->
          <div
            v-if="logStore.selectedLogLevel !== 'all'"
            class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200 border border-green-200 dark:border-green-700"
          >
            <component :is="AlertCircle" class="w-3 h-3 mr-1.5" />
            级别: {{ getLevelDisplayName(logStore.selectedLogLevel) }}
            <button
              @click="logStore.setLogLevel('all')"
              class="ml-2 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200 transition-colors"
            >
              <component :is="X" class="w-3 h-3" />
            </button>
          </div>

          <!-- 来源过滤器 -->
          <div
            v-if="logStore.selectedSources.length > 0"
            class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200 border border-purple-200 dark:border-purple-700"
          >
            <component :is="Database" class="w-3 h-3 mr-1.5" />
            来源: {{ logStore.selectedSources.length }} 个
            <button
              @click="logStore.setSelectedSources([])"
              class="ml-2 text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-200 transition-colors"
            >
              <component :is="X" class="w-3 h-3" />
            </button>
          </div>

          <!-- 时间范围过滤器 -->
          <div
            v-if="logStore.selectedTimeRange !== '24h'"
            class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200 border border-orange-200 dark:border-orange-700"
          >
            <component :is="Clock" class="w-3 h-3 mr-1.5" />
            时间: {{ getTimeRangeDisplayName(logStore.selectedTimeRange) }}
            <button
              @click="logStore.setTimeRange('24h')"
              class="ml-2 text-orange-600 hover:text-orange-800 dark:text-orange-400 dark:hover:text-orange-200 transition-colors"
            >
              <component :is="X" class="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- 过滤结果统计 -->
      <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700/50">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-blue-600 dark:text-blue-400">过滤结果</p>
            <p class="text-2xl font-bold text-blue-900 dark:text-blue-100 mt-1">
              {{ logStore.filteredLogs.length.toLocaleString() }}
            </p>
            <p class="text-xs text-blue-500 dark:text-blue-400 mt-1">
              共 {{ logStore.logs.length.toLocaleString() }} 条日志
            </p>
          </div>
          <div class="p-3 bg-blue-500/10 rounded-lg">
            <component :is="FileText" class="w-6 h-6 text-blue-500" />
          </div>
        </div>
      </div>

      <!-- 错误统计 -->
      <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-xl p-6 border border-red-200 dark:border-red-700/50">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-red-600 dark:text-red-400">错误日志</p>
            <p class="text-2xl font-bold text-red-900 dark:text-red-100 mt-1">
              {{ logStore.logLevelCounts.error.toLocaleString() }}
            </p>
            <p class="text-xs text-red-500 dark:text-red-400 mt-1">
              {{ getErrorRate() }}% 错误率
            </p>
          </div>
          <div class="p-3 bg-red-500/10 rounded-lg">
            <component :is="AlertTriangle" class="w-6 h-6 text-red-500" />
          </div>
        </div>
      </div>

      <!-- 警告统计 -->
      <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-xl p-6 border border-yellow-200 dark:border-yellow-700/50">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-yellow-600 dark:text-yellow-400">警告日志</p>
            <p class="text-2xl font-bold text-yellow-900 dark:text-yellow-100 mt-1">
              {{ logStore.logLevelCounts.warn.toLocaleString() }}
            </p>
            <p class="text-xs text-yellow-500 dark:text-yellow-400 mt-1">
              {{ getWarnRate() }}% 警告率
            </p>
          </div>
          <div class="p-3 bg-yellow-500/10 rounded-lg">
            <component :is="AlertCircle" class="w-6 h-6 text-yellow-500" />
          </div>
        </div>
      </div>

      <!-- 实时状态 -->
      <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-6 border border-green-200 dark:border-green-700/50">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-green-600 dark:text-green-400">实时模式</p>
            <div class="flex items-center space-x-2 mt-1">
              <div
                class="w-3 h-3 rounded-full"
                :class="logStore.isRealTimeMode ? 'bg-green-500 animate-pulse' : 'bg-gray-400'"
              ></div>
              <span class="text-xl font-bold text-green-900 dark:text-green-100">
                {{ logStore.isRealTimeMode ? '实时' : '暂停' }}
              </span>
            </div>
            <p class="text-xs text-green-500 dark:text-green-400 mt-1">
              {{ logStore.isRealTimeMode ? '正在监听新日志' : '点击开始监听' }}
            </p>
          </div>
          <div class="p-3 bg-green-500/10 rounded-lg">
            <component :is="Activity" class="w-6 h-6 text-green-500" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 高级过滤器 -->
    <AdvancedFilter />

    <!-- 日志内容区域 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <!-- 工具栏 -->
      <div class="bg-gray-50 dark:bg-gray-800/50 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <component :is="FileText" class="w-5 h-5 mr-2 text-blue-500" />
              日志条目
            </h3>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              显示 {{ getDisplayRange() }} 条，共 {{ logStore.filteredLogs.length.toLocaleString() }} 条
            </div>
          </div>

          <div class="flex items-center space-x-3">
            <!-- 每页显示数量 -->
            <div class="flex items-center space-x-2">
              <label class="text-sm text-gray-600 dark:text-gray-400">每页:</label>
              <select
                v-model="logStore.pageSize"
                class="appearance-none bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-1.5 pr-8 text-sm font-medium text-gray-700 dark:text-gray-200 hover:border-blue-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all duration-200"
              >
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
                <option :value="200">200</option>
                <option :value="500">500</option>
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <component :is="ChevronDown" class="w-4 h-4 text-gray-400" />
              </div>
            </div>

            <!-- 刷新按钮 -->
            <button
              @click="refreshLogs"
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
              :disabled="isRefreshing"
            >
              <component :is="RefreshCw" :class="['w-4 h-4 mr-2', isRefreshing ? 'animate-spin' : '']" />
              刷新
            </button>

            <!-- 导出按钮 -->
            <button
              @click="exportLogs"
              class="inline-flex items-center px-4 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200"
            >
              <component :is="Download" class="w-4 h-4 mr-2" />
              导出
            </button>
          </div>
        </div>
      </div>

      <!-- 日志内容 -->
      <div class="relative">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="absolute inset-0 bg-white/50 dark:bg-gray-800/50 flex items-center justify-center z-10">
          <div class="flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">加载中...</span>
          </div>
        </div>

        <!-- 视图切换内容 -->
        <div v-if="viewMode === 'table'">
          <LogList />
        </div>
        <div v-else-if="viewMode === 'terminal'" class="h-96">
          <VirtualTerminal />
        </div>
      </div>

      <!-- 分页器 -->
      <div v-if="logStore.totalPages > 1" class="bg-gray-50 dark:bg-gray-800/50 px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <Pagination />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useLogStore } from '@/stores/logStore'
import { useAppStore } from '@/stores/appStore'
import { generateRealtimeLog } from '@/utils/mockData'
import {
  X,
  Download,
  ChevronDown,
  Table,
  Terminal,
  Filter,
  Search,
  AlertCircle,
  Database,
  Clock,
  FileText,
  AlertTriangle,
  Activity,
  RefreshCw
} from 'lucide-vue-next'

import SearchBar from '@/components/SearchBar.vue'
import SourceFilter from '@/components/SourceFilter.vue'
import LogList from '@/components/LogList.vue'
import Pagination from '@/components/Pagination.vue'
import AdvancedFilter from '@/components/AdvancedFilter.vue'
import VirtualTerminal from '@/components/VirtualTerminal.vue'

const logStore = useLogStore()
const appStore = useAppStore()

// 组件状态
const viewMode = ref('table') // 'table' | 'terminal'
const isLoading = ref(false)
const isRefreshing = ref(false)

let realtimeInterval = null

// 计算属性
const hasActiveFilters = computed(() => {
  return logStore.searchQuery ||
         logStore.selectedLogLevel !== 'all' ||
         logStore.selectedSources.length > 0 ||
         logStore.selectedTimeRange !== '24h'
})

// 辅助方法
const getLevelDisplayName = (level) => {
  const names = {
    error: '🔴 错误',
    warn: '🟡 警告',
    info: '🔵 信息',
    debug: '⚪ 调试',
    trace: '🟣 追踪'
  }
  return names[level] || level
}

const getTimeRangeDisplayName = (range) => {
  const names = {
    '1h': '最近1小时',
    '6h': '最近6小时',
    '24h': '最近24小时',
    '7d': '最近7天'
  }
  return names[range] || range
}

const getErrorRate = () => {
  const total = logStore.logs.length
  if (total === 0) return 0
  return Math.round((logStore.logLevelCounts.error / total) * 100)
}

const getWarnRate = () => {
  const total = logStore.logs.length
  if (total === 0) return 0
  return Math.round((logStore.logLevelCounts.warn / total) * 100)
}

const getDisplayRange = () => {
  const start = (logStore.currentPage - 1) * logStore.pageSize + 1
  const end = Math.min(logStore.currentPage * logStore.pageSize, logStore.filteredLogs.length)
  return `${start}-${end}`
}

// 操作方法
const clearFilters = () => {
  logStore.setSearchQuery('')
  logStore.setLogLevel('all')
  logStore.setSelectedSources([])
  logStore.setTimeRange('24h')
}

const refreshLogs = async () => {
  isRefreshing.value = true
  try {
    // 模拟刷新延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 这里可以添加实际的刷新逻辑
    appStore.addNotification({
      type: 'success',
      message: '日志已刷新'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '刷新失败: ' + error.message
    })
  } finally {
    isRefreshing.value = false
  }
}

const exportLogs = () => {
  const logs = logStore.filteredLogs
  const csvContent = [
    'Timestamp,Level,Source,Service,Message',
    ...logs.map(log => 
      `"${log.timestamp}","${log.level}","${log.source}","${log.service}","${log.message.replace(/"/g, '""')}"`
    )
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `logs-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
  
  appStore.addNotification({
    type: 'success',
    message: `已导出 ${logs.length} 条日志记录`
  })
}

const startRealtimeUpdates = () => {
  if (realtimeInterval) return
  
  realtimeInterval = setInterval(() => {
    if (logStore.isRealTimeMode) {
      // Simulate receiving new logs
      const newLog = generateRealtimeLog()
      logStore.addLog(newLog)
    }
  }, 2000) // Add new log every 2 seconds
}

const stopRealtimeUpdates = () => {
  if (realtimeInterval) {
    clearInterval(realtimeInterval)
    realtimeInterval = null
  }
}

onMounted(() => {
  startRealtimeUpdates()
})

onUnmounted(() => {
  stopRealtimeUpdates()
})
</script>
